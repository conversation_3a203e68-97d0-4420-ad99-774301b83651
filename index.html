<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sem Enkelmans - ICT Student Portfolio</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="#home">Sem <PERSON>kelmans</a>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="#home" class="nav-link">Home</a>
                <a href="#about" class="nav-link">About</a>
                <a href="#skills" class="nav-link">Skills</a>
                <a href="#projects" class="nav-link">Projects</a>
                <a href="#contact" class="nav-link">Contact</a>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>Hello, I'm <span class="highlight">Sem Enkelmans</span></h1>
            <h2>ICT Student & Aspiring Developer</h2>
            <p>Passionate about technology and creating innovative solutions</p>
            <div class="hero-buttons">
                <a href="#projects" class="btn btn-primary">View My Work</a>
                <a href="#contact" class="btn btn-secondary">Get In Touch</a>
            </div>
        </div>
        <div class="hero-image">
            <div class="placeholder-avatar">
                <i class="fas fa-user"></i>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-content">
                <div class="about-text">
                    <p>I am a dedicated ICT student with a passion for technology and problem-solving. Currently pursuing my studies in Information and Communication Technology, I am eager to apply my knowledge in real-world scenarios and contribute to innovative projects.</p>
                    
                    <p>My journey in ICT has equipped me with a solid foundation in various programming languages, database management, and system administration. I am constantly learning and adapting to new technologies to stay current with industry trends.</p>
                    
                    <div class="about-stats">
                        <div class="stat">
                            <h3>1+</h3>
                            <p>Years of Study</p>
                        </div>
                        <div class="stat">
                            <h3>15+</h3>
                            <p>Projects Completed</p>
                        </div>
                        <div class="stat">
                            <h3>5+</h3>
                            <p>Technologies Learned</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title">Technical Skills</h2>
            <div class="skills-grid">
                <div class="skill-category">
                    <h3><i class="fas fa-code"></i> Programming Languages</h3>
                    <div class="skill-items">
                        <span class="skill-tag">JavaScript</span>
                        <span class="skill-tag">Python</span>
                        <span class="skill-tag">Java</span>
                        <span class="skill-tag">C#</span>
                        <span class="skill-tag">PHP</span>
                        <span class="skill-tag">SQL</span>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3><i class="fas fa-laptop-code"></i> Web Technologies</h3>
                    <div class="skill-items">
                        <span class="skill-tag">HTML5</span>
                        <span class="skill-tag">CSS3</span>
                        <span class="skill-tag">React</span>
                        <span class="skill-tag">Node.js</span>
                        <span class="skill-tag">Bootstrap</span>
                        <span class="skill-tag">REST APIs</span>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3><i class="fas fa-database"></i> Databases & Tools</h3>
                    <div class="skill-items">
                        <span class="skill-tag">MySQL</span>
                        <span class="skill-tag">PostgreSQL</span>
                        <span class="skill-tag">MongoDB</span>
                        <span class="skill-tag">Git</span>
                        <span class="skill-tag">Docker</span>
                        <span class="skill-tag">VS Code</span>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3><i class="fas fa-server"></i> Systems & Networks</h3>
                    <div class="skill-items">
                        <span class="skill-tag">Linux</span>
                        <span class="skill-tag">Windows Server</span>
                        <span class="skill-tag">Network Configuration</span>
                        <span class="skill-tag">Cloud Services</span>
                        <span class="skill-tag">Cybersecurity</span>
                        <span class="skill-tag">Virtualization</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title">My Projects</h2>
            <p class="section-subtitle">Here are some of the projects I've worked on during my studies</p>
            
            <div class="projects-grid">
                <!-- Project 1 Placeholder -->
                <div class="project-card">
                    <div class="project-image">
                        <img src="cvservicezuid.png" alt="CV Service Zuid Website Screenshot">
                    </div>
                    <div class="project-content">
                        <h3>CV Service Zuid</h3>
                        <p>I used react to create a website for CV Service Zuid. CV Service Zuid is a company that fixes, installs and maintains central heating systems.</p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">Hostinge</span>
                        </div>
                        <div class="project-links">
                            <a href="https://linen-swan-880585.hostingersite.com/" class="project-link" target="_blank">
                                <i class="fas fa-external-link-alt"></i> Live Demo
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project 2 Placeholder -->
                <div class="project-card">
                    <div class="project-image">
                        <img src="bouwservice.png" alt="Bouwservice R. Beijer Website Screenshot">
                    </div>
                    <div class="project-content">
                        <h3>Bouwservice R. Beijer</h3>
                        <p>Bouwservice R. Beijer is a company that does all kinds of building work. I created a website for them using React, node.js and hostinger.</p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">Hostinger</span>
                        </div>
                        <div class="project-links">
                            <a href="https://saddlebrown-chicken-869978.hostingersite.com/" class="project-link" target="_blank">
                                <i class="fas fa-external-link-alt"></i> Live Demo
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project 3 Placeholder -->
                <div class="project-card">
                    <div class="project-image">
                        <img src="josophelders.png" alt="Jos Ophelders Tegelzettersbedrijf Website Screenshot">
                    </div>
                    <div class="project-content">
                        <h3>Jos Ophelders, Tegelzettersbedrijf</h3>
                        <p>Jos Ophelders is a company that does all kinds of tile work. I created a website for them using React, node.js and hostinger.</p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">Hostinger</span>
                        </div>
                        <div class="project-links">
                            <a href="https://royalblue-tapir-420731.hostingersite.com/" class="project-link" target="_blank">
                                <i class="fas fa-external-link-alt"></i> Live Demo
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Get In Touch</h2>
            <p class="section-subtitle">I'm always open to discussing new opportunities and collaborations</p>
            
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h3>Email</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h3>Phone</h3>
                            <p>+31 06 83 18 37 21</p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h3>Location</h3>
                            <p>Sittard, Netherlands</p>
                        </div>
                    </div>
                    
                    <div class="social-links">
                        <a href="https://www.linkedin.com/in/sem-enkelmans/" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://github.com/enkelmansdigital" target="_blank"><i class="fab fa-github"></i></a>
                    </div>
                </div>
                
                <form class="contact-form" id="contact-form">
                    <div class="form-group">
                        <input type="text" id="name" name="name" placeholder="Your Name" required>
                    </div>
                    <div class="form-group">
                        <input type="email" id="email" name="email" placeholder="Your Email" required>
                    </div>
                    <div class="form-group">
                        <input type="text" id="subject" name="subject" placeholder="Subject" required>
                    </div>
                    <div class="form-group">
                        <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Send Message</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Sem Enkelmans. All rights reserved.</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
